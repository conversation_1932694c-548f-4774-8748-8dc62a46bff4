Stack trace:
Frame         Function      Args
0007FFFFA170  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9070) msys-2.0.dll+0x1FE8E
0007FFFFA170  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA448) msys-2.0.dll+0x67F9
0007FFFFA170  000210046832 (000210286019, 0007FFFFA028, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA170  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA170  000210068E24 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA450  00021006A225 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF9B360000 ntdll.dll
7FFF9AD20000 KERNEL32.DLL
7FFF988B0000 KERNELBASE.dll
7FFF94FB0000 apphelp.dll
7FFF99160000 USER32.dll
7FFF98E90000 win32u.dll
000210040000 msys-2.0.dll
7FFF99F00000 GDI32.dll
7FFF99020000 gdi32full.dll
7FFF98540000 msvcp_win.dll
7FFF98760000 ucrtbase.dll
7FFF9B260000 advapi32.dll
7FFF99E40000 msvcrt.dll
7FFF99C30000 sechost.dll
7FFF9A110000 RPCRT4.dll
7FFF97A40000 CRYPTBASE.DLL
7FFF98F80000 bcryptPrimitives.dll
7FFF99B80000 IMM32.DLL
