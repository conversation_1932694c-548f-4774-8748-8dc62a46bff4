import 'package:flutter/material.dart';
import 'package:managment_system/models/chef.dart';
import 'package:managment_system/services/chef_services.dart';
import 'package:managment_system/utils/app_theme.dart';

import 'add_chef_screen.dart';

class ChefsScreen extends StatefulWidget {
  const ChefsScreen({Key? key}) : super(key: key);

  @override
  State<ChefsScreen> createState() => _ChefsScreenState();
}

class _ChefsScreenState extends State<ChefsScreen> {
  late Future<List<Chef>> _chefsFuture;
  final ChefServices _chefService = ChefServices();

  @override
  void initState() {
    super.initState();
    _chefsFuture = _chefService.getChefs();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Chefs Management',
          style: AppTheme.titleLargeStyle.copyWith(
            color: AppTheme.textOnPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: AppTheme.textOnPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: () {
              setState(() {
                _chefsFuture = _chefService.getChefs();
              });
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Header section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppTheme.primaryColor.withAlpha(10),
                  AppTheme.backgroundColor,
                ],
              ),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryExtraLight,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.people_rounded,
                    size: 48,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Chef Management',
                  style: AppTheme.headingStyle,
                ),
                const SizedBox(height: 8),
                Text(
                  'Manage your team of chefs and their specialties',
                  style: AppTheme.captionStyle,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Chefs list
          Expanded(
            child: FutureBuilder<List<Chef>>(
              future: _chefsFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return AppTheme.buildLoadingWidget(message: 'Loading chefs...');
                } else if (snapshot.hasError) {
                  return AppTheme.buildEmptyState(
                    icon: Icons.error_outline_rounded,
                    title: 'Error loading chefs',
                    subtitle: 'Please try again later',
                    action: ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          _chefsFuture = _chefService.getChefs();
                        });
                      },
                      icon: const Icon(Icons.refresh_rounded),
                      label: const Text('Retry'),
                      style: AppTheme.primaryButtonStyle,
                    ),
                  );
                } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  return ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: snapshot.data!.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final chef = snapshot.data![index];
                      return AppTheme.buildCard(
                        elevated: true,
                        child: Row(
                          children: [
                            // Chef avatar
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: AppTheme.primaryExtraLight,
                                borderRadius: BorderRadius.circular(30),
                              ),
                              child: Icon(
                                Icons.person_rounded,
                                size: 32,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                            const SizedBox(width: 16),

                            // Chef details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    chef.name,
                                    style: AppTheme.titleStyle.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    chef.email,
                                    style: AppTheme.captionStyle,
                                  ),
                                  const SizedBox(height: 8),

                                  // Specialties chips
                                  Wrap(
                                    spacing: 6,
                                    runSpacing: 4,
                                    children: chef.speciality.map((specialty) {
                                      return Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: AppTheme.accentColor.withAlpha(20),
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(
                                            color: AppTheme.accentColor.withAlpha(100),
                                          ),
                                        ),
                                        child: Text(
                                          specialty,
                                          style: AppTheme.captionStyle.copyWith(
                                            color: AppTheme.accentDarkColor,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ],
                              ),
                            ),

                            // Action button
                            Icon(
                              Icons.chevron_right_rounded,
                              color: AppTheme.textLightColor,
                              size: 20,
                            ),
                          ],
                        ),
                      );
                    },
                  );
                } else {
                  return AppTheme.buildEmptyState(
                    icon: Icons.people_outline_rounded,
                    title: 'No chefs found',
                    subtitle: 'Add your first chef to get started',
                    action: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => AddChefScreen()),
                        );
                      },
                      icon: const Icon(Icons.add_rounded),
                      label: const Text('Add First Chef'),
                      style: AppTheme.primaryButtonStyle,
                    ),
                  );
                }
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AddChefScreen()),
          ).then((_) {
            // Refresh the list when returning from add chef screen
            setState(() {
              _chefsFuture = _chefService.getChefs();
            });
          });
        },
        label: const Text('Add Chef'),
        icon: const Icon(Icons.add_rounded),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: AppTheme.textOnPrimary,
      ),
    );
  }
}
