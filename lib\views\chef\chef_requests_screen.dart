import 'package:flutter/material.dart';
import '../../services/stock_request_services.dart';
import '../../models/stock_request.dart';
import '../../utils/app_theme.dart';
import '../../services/auth_service.dart';
import '../../services/local_storage.dart';
import '../../models/user.dart';

class ChefRequestsScreen extends StatefulWidget {
  const ChefRequestsScreen({Key? key}) : super(key: key);

  @override
  State<ChefRequestsScreen> createState() => _ChefRequestsScreenState();
}

class _ChefRequestsScreenState extends State<ChefRequestsScreen> {
  final StockRequestService _stockRequestService = StockRequestService();
  final AuthService _authService = AuthService();
  List<StockRequest> _requests = [];
  bool _isLoading = false;
  User? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      String? token = await LocalStorage.getToken();
      if (token != null) {
        User? user = await _authService.getUserByToken(token);
        setState(() {
          _currentUser = user;
        });
        if (user != null) {
          _loadStockRequests();
        }
      }
    } catch (e) {
      print("Error loading current user: $e");
    }
  }

  Future<void> _loadStockRequests() async {
    if (_currentUser == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Load all stock requests for chef to review
      final requests = await _stockRequestService.getAllStockRequestsForAdmin();
      setState(() {
        _requests = requests;
      });
    } catch (e) {
      print("Error loading stock requests: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load requests: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updateRequestStatus(int requestId, String status) async {
    try {
      await _stockRequestService.updateStockRequestStatus(requestId, status);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Request ${status.toLowerCase()} successfully'),
          backgroundColor: status == 'approved' ? AppTheme.successColor : AppTheme.warningColor,
        ),
      );
      _loadStockRequests(); // Refresh the list
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update request: $e'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppTheme.warningColor;
      case 'approved':
      case 'accepted':
        return AppTheme.successColor;
      case 'rejected':
        return AppTheme.errorColor;
      default:
        return AppTheme.textSecondaryColor;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.hourglass_empty_rounded;
      case 'approved':
      case 'accepted':
        return Icons.check_circle_rounded;
      case 'rejected':
        return Icons.cancel_rounded;
      default:
        return Icons.help_outline_rounded;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Stock Requests',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textOnPrimary,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _loadStockRequests,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadStockRequests,
        color: AppTheme.primaryColor,
        child: _isLoading
            ? AppTheme.buildLoadingWidget(message: 'Loading requests...')
            : _requests.isEmpty
                ? AppTheme.buildEmptyState(
                    icon: Icons.inbox_outlined,
                    title: 'No requests found',
                    subtitle: 'Stock requests will appear here when submitted',
                  )
                : ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: _requests.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final request = _requests[index];
                      return AppTheme.buildCard(
                        elevated: true,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header with request info and status
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryExtraLight,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.inventory_2_rounded,
                                    color: AppTheme.primaryColor,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Request #${request.id}',
                                        style: AppTheme.titleMediumStyle.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        request.stockType,
                                        style: AppTheme.bodyLargeStyle.copyWith(
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(request.status).withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: _getStatusColor(request.status).withOpacity(0.3),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _getStatusIcon(request.status),
                                        size: 16,
                                        color: _getStatusColor(request.status),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        request.status.toUpperCase(),
                                        style: AppTheme.captionStyle.copyWith(
                                          color: _getStatusColor(request.status),
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 16),

                            // Description
                            if (request.description.isNotEmpty) ...[
                              Text(
                                'Description:',
                                style: AppTheme.bodyMediumStyle.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                request.description,
                                style: AppTheme.bodyMediumStyle.copyWith(
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                            ],

                            // Request details
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time_rounded,
                                  size: 16,
                                  color: AppTheme.textSecondaryColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Requested: ${_formatDate(request.createdAt)}',
                                  style: AppTheme.captionStyle.copyWith(
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),

                            // Action buttons for pending requests
                            if (request.status.toLowerCase() == 'pending') ...[
                              const SizedBox(height: 16),
                              const Divider(),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextButton.icon(
                                      onPressed: () => _updateRequestStatus(request.id!, 'rejected'),
                                      icon: const Icon(Icons.close_rounded),
                                      label: const Text('Reject'),
                                      style: TextButton.styleFrom(
                                        foregroundColor: AppTheme.errorColor,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () => _updateRequestStatus(request.id!, 'approved'),
                                      icon: const Icon(Icons.check_rounded),
                                      label: const Text('Approve'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: AppTheme.successColor,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      );
                    },
                  ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
