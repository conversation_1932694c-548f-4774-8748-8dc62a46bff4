import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:managment_system/utils/app_theme.dart';
import 'package:managment_system/views/splash_screen.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Management System',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.getThemeData(),
      home: const SplashScreen(),
    );
  }
}


