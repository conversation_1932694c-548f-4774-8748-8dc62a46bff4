import 'package:flutter/material.dart';
import '../../services/question_services.dart'; // Import QuestionService
import '../../models/question.dart'; // Import Question Model
import '../../utils/app_theme.dart'; // Import AppTheme
import 'question_detail_screen.dart'; // Import QuestionDetailScreen

class QuestionScreen extends StatefulWidget {
  @override
  _QuestionScreenState createState() => _QuestionScreenState();
}

class _QuestionScreenState extends State<QuestionScreen> {
  final TextEditingController _questionController = TextEditingController();
  bool _isLoading = false;
  final QuestionService _questionService = QuestionService();
  List<Question> _userQuestions = []; // Use Question model for list



  @override
  void initState() {
    super.initState();
    _loadUserQuestions(); // Load questions when screen initializes
  }

  Future<void> _loadUserQuestions() async {
    setState(() {
      _isLoading = true;
    });
    try {
      _userQuestions = await _questionService.getUserQuestions();
    } catch (e) {
      // Handle error loading questions
      print('Error loading questions: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading questions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _askQuestion() async {
    final questionText = _questionController.text.trim();

    if (questionText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a question'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final question = await _questionService.askQuestion(questionText);

      // Clear the question input field after successful submission
      _questionController.clear();

      // Reload questions after asking a new one
      await _loadUserQuestions();

      // Navigate to question detail screen to show the result
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QuestionDetailScreen(question: question),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'My Questions',
          style: AppTheme.titleLargeStyle.copyWith(
            color: AppTheme.textOnPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: AppTheme.textOnPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _loadUserQuestions,
            tooltip: 'Refresh',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showQuestionBottomSheet,
        tooltip: 'Ask a question',
        icon: const Icon(Icons.add_rounded),
        label: const Text('Ask Question'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: AppTheme.textOnPrimary,
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Expanded(
                    child: _userQuestions.isEmpty
                        ? AppTheme.buildEmptyState(
                            icon: Icons.help_outline_rounded,
                            title: 'No questions yet',
                            subtitle: 'Tap the "Ask Question" button to get started',
                            action: ElevatedButton.icon(
                              onPressed: _showQuestionBottomSheet,
                              icon: const Icon(Icons.add_rounded),
                              label: const Text('Ask Your First Question'),
                              style: AppTheme.primaryButtonStyle,
                            ),
                          )
                        : ListView.separated(
                            padding: const EdgeInsets.all(16),
                            itemCount: _userQuestions.length,
                            separatorBuilder: (context, index) => const SizedBox(height: 12),
                            itemBuilder: (context, index) {
                              final question = _userQuestions[index];
                              return AppTheme.buildCard(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => QuestionDetailScreen(
                                        question: question,
                                      ),
                                    ),
                                  );
                                },
                                elevated: true,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Question text
                                    Text(
                                      question.question,
                                      style: AppTheme.titleStyle.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 12),

                                    // Category and status chips
                                    Row(
                                      children: [
                                        // Category chip
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 12,
                                            vertical: 6,
                                          ),
                                          decoration: BoxDecoration(
                                            color: AppTheme.primaryExtraLight,
                                            borderRadius: BorderRadius.circular(16),
                                            border: Border.all(
                                              color: AppTheme.primaryLightColor,
                                              width: 1,
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.category_rounded,
                                                size: 14,
                                                color: AppTheme.primaryColor,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                question.type ?? 'Unknown',
                                                style: AppTheme.captionStyle.copyWith(
                                                  color: AppTheme.primaryColor,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(width: 8),

                                        // Status chip
                                        AppTheme.buildStatusChip(question.status),

                                        const Spacer(),

                                        // Arrow icon
                                        Icon(
                                          Icons.chevron_right_rounded,
                                          color: AppTheme.textLightColor,
                                          size: 20,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  void _showQuestionBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              decoration: const BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                  left: 24,
                  right: 24,
                  top: 24,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Handle bar
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: AppTheme.borderColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryExtraLight,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.help_outline_rounded,
                            color: AppTheme.primaryColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Ask a Question",
                                style: AppTheme.titleLargeStyle.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                "Get help from our AI assistant",
                                style: AppTheme.captionStyle,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Question input
                    TextField(
                      controller: _questionController,
                      decoration: AppTheme.inputDecoration(
                        'Your Question',
                        hint: 'Describe your question in detail...',
                        prefixIcon: const Icon(Icons.edit_rounded),
                      ),
                      maxLines: 4,
                      textInputAction: TextInputAction.done,
                      style: AppTheme.bodyStyle,
                    ),
                    const SizedBox(height: 24),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: AppTheme.secondaryButtonStyle,
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: _isLoading
                                ? null
                                : () async {
                                    Navigator.pop(context);
                                    await _askQuestion();
                                  },
                            style: AppTheme.primaryButtonStyle,
                            child: _isLoading
                                ? SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        AppTheme.textOnPrimary,
                                      ),
                                    ),
                                  )
                                : const Text('Ask Question'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
