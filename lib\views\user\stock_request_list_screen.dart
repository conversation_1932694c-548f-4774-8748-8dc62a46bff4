import 'package:flutter/material.dart';
import 'package:managment_system/models/stock_request.dart';
import 'package:managment_system/services/stock_request_services.dart';
import 'package:managment_system/utils/local_storage.dart'; // For user ID
import 'package:managment_system/utils/app_theme.dart';

import '../../models/user.dart';
import '../../services/authentication.dart' show AuthenticationServices;
import 'add_stock_request_screen.dart'; // To navigate to add request screen

class StockRequestListScreen extends StatefulWidget {
  const StockRequestListScreen({Key? key}) : super(key: key);

  @override
  _StockRequestListScreenState createState() => _StockRequestListScreenState();
}

class _StockRequestListScreenState extends State<StockRequestListScreen> {
  final StockRequestService _stockRequestService = StockRequestService();
  final AuthenticationServices _authService = AuthenticationServices();

  List<StockRequest> _stockRequests = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStockRequests();
  }

  Future<void> _loadStockRequests() async {
    setState(() {
      _isLoading = true;
    });
    try {
      // --- Get User ID (Placeholder - Adapt as needed) ---
      String? token = await LocalStorage.getToken();
      print("token: $token");
      User? user = await _authService.getUserByToken(token!); // Example call
      if (user == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Could not get user ID. Please log in again.')),
        );
        return;
      }
      // --- End Get User ID ---

      _stockRequests =
          await _stockRequestService.getAllStockRequestsForUser(user.id);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading stock requests: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'My Stock Requests',
          style: AppTheme.titleLargeStyle.copyWith(
            color: AppTheme.textOnPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: AppTheme.textOnPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _loadStockRequests,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadStockRequests,
        color: AppTheme.primaryColor,
        child: _isLoading
            ? AppTheme.buildLoadingWidget(message: 'Loading your requests...')
            : _stockRequests.isEmpty
                ? AppTheme.buildEmptyState(
                    icon: Icons.inventory_2_outlined,
                    title: 'No stock requests yet',
                    subtitle: 'Tap the "Request Stock" button to create your first request',
                    action: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => AddStockRequestScreen()),
                        ).then((_) {
                          _loadStockRequests();
                        });
                      },
                      icon: const Icon(Icons.add_rounded),
                      label: const Text('Request Stock'),
                      style: AppTheme.primaryButtonStyle,
                    ),
                  )
                : ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: _stockRequests.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final request = _stockRequests[index];
                      return AppTheme.buildCard(
                        elevated: true,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header with stock type and status
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryExtraLight,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.inventory_2_rounded,
                                    color: AppTheme.primaryColor,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        request.stockType,
                                        style: AppTheme.titleStyle.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Request #${request.id}',
                                        style: AppTheme.captionStyle,
                                      ),
                                    ],
                                  ),
                                ),
                                AppTheme.buildStatusChip(request.status),
                              ],
                            ),

                            if (request.description.isNotEmpty) ...[
                              const SizedBox(height: 16),
                              Text(
                                'Description',
                                style: AppTheme.labelStyle,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                request.description,
                                style: AppTheme.bodyStyle,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],

                            const SizedBox(height: 16),

                            // Footer with date and action
                            Row(
                              children: [
                                Icon(
                                  Icons.schedule_rounded,
                                  size: 16,
                                  color: AppTheme.textSecondaryColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Created ${_formatDate(request.createdAt)}',
                                  style: AppTheme.captionStyle,
                                ),
                                const Spacer(),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: AppTheme.textLightColor,
                                  size: 20,
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AddStockRequestScreen()),
          ).then((_) {
            _loadStockRequests();
          });
        },
        label: const Text('Request Stock'),
        icon: const Icon(Icons.add_rounded),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: AppTheme.textOnPrimary,
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }
}
